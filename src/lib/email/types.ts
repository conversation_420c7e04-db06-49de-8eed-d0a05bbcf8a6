/**
 * Email notification types and interfaces
 */

export type NotificationType = 
  | 'auction_closed'
  | 'new_bid_received'
  | 'auction_winner_selected'
  | 'auction_started';

export interface BaseNotificationData {
  type: NotificationType;
  auctionId: string;
  recipientEmail: string;
  recipientName?: string;
}

export interface AuctionClosedNotificationData extends BaseNotificationData {
  type: 'auction_closed';
  policyNumber: string;
  assetDisplayName: string;
  totalBids: number;
  closedAt: string;
  accountHolderName: string;
}

export interface NewBidNotificationData extends BaseNotificationData {
  type: 'new_bid_received';
  policyNumber: string;
  assetDisplayName: string;
  bidAmount: number;
  brokerName: string;
  timeRemaining: string;
  accountHolderName: string;
}

export interface AuctionWinnerNotificationData extends BaseNotificationData {
  type: 'auction_winner_selected';
  policyNumber: string;
  assetDisplayName: string;
  winningBidAmount: number;
  position: number; // 1st, 2nd, or 3rd place
  brokerName: string;
}

export interface AuctionStartedNotificationData extends BaseNotificationData {
  type: 'auction_started';
  policyNumber: string;
  assetDisplayName: string;
  startDate: string;
  endDate: string;
  accountHolderName: string;
}

export type NotificationData = 
  | AuctionClosedNotificationData
  | NewBidNotificationData
  | AuctionWinnerNotificationData
  | AuctionStartedNotificationData;

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface NotificationQueueItem {
  id: string;
  data: NotificationData;
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  scheduledFor?: string;
}
