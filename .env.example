# Site URL for auth redirects
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Connect to Supabase via connection pooling
DATABASE_URL=""

# Direct connection to the database. Used for migrations
DIRECT_URL=""

# Supabase
NEXT_PUBLIC_SUPABASE_URL=""
NEXT_PUBLIC_SUPABASE_ANON_KEY=""

# You need to add your service role key from the Supabase dashboard
SUPABASE_SERVICE_ROLE_KEY=""
SUPABASE_ACCESS_TOKEN=""

# Google Gemini
GEMINI_API_KEY=""
GEMINI_MODEL="gemini-2.5-flash-lite-preview-06-17"

# Cloudflare R2 for zeeguros-platform bucket
R2_ACCOUNT_ID=""
R2_ACCESS_KEY_ID=""
R2_SECRET_ACCESS_KEY=""
R2_BUCKET_NAME=""
R2_PUBLIC_URL=""

# Brevo SMTP for email notifications
BREVO_API_KEY=""
BREVO_SENDER_EMAIL="<EMAIL>"
BREVO_SENDER_NAME="Zeeguros"
